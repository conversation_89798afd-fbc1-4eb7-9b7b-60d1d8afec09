import 'package:daleel/features/main_taps/screens/main_taps/presentation/screen/main_taps_screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'features/login/presentation/screen/login_screen.dart';
import 'features/splash/splash_screen.dart';


class AppRouter {
  /// main Navigation Router
  static final GlobalKey<NavigatorState> mainNavigatorKey =
      GlobalKey<NavigatorState>();

  static GoRouter router = GoRouter(
    debugLogDiagnostics: true,
    navigatorKey: mainNavigatorKey,
    initialLocation: SplashScreen.routeName,
    routes: <RouteBase>[
      GoRoute(
        path: '/',
        redirect: (context, state) => SplashScreen.routeName,
      ),
      GoRoute(
        path: SplashScreen.routeName,
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: LoginScreen.routeName,
        builder: (context, state) => LoginScreen(),
      ),
      GoRoute(
        path: MainTapsScreen.routeName,
        builder: (context, state) => const MainTapsScreen(),
      ),
    ],
  );
}
