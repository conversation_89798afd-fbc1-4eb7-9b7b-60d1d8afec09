class ApiKeys {
  /// KEYs
  static const authorization = "Authorization";
  static const accept = "Accept";
  static const applicationJson = "application/json";
  static const locale = "Accept-Language";
  static const contentType = "Content-Type";
  static const keyBearer = "Bearer";
  static const platform = "Platform";
  static const platformAndroid = "android";
  static const platformIos = "ios";
  static const appVersion = "App-Version";
  static const perPageKey = "per_page";
  static const pageNumberKey = "page";
  static const perPageValue = 10;
  static const pageIndex = "PageIndex";
  static const pageSize = "PageSize";
  static const keyWord = "keyword";
  static const bearer = "Bearer";
  static const accessToken = "accessToken";
  static const refreshToken = "refreshToken";
  static const organizationIds = "organizationIds";
  static const categoryIds = "categoryIds";
  static const targetAudienceIds = "targetAudianceIds";
  static const serviceTypeIds = "serviceTypeIds";
  static const minAge = "minAge";
  static const maxAge = "maxAge";
  static const orderByMostUsedServices = "orderByMostUsedServices";
  static const userId = "userId";
  static const search = "SearchValue";
  static const history = "History";
  static const leaveTypeId = "leaveTypeId";
  static const from = "from";
  static const to = "to";
  static const draft = "draft";
  static const id = "id";
  static const type = "type";
}
