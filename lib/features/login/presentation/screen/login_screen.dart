import 'package:daleel/app_router.dart';
import 'package:daleel/features/main_taps/screens/main_taps/presentation/screen/main_taps_screen.dart';
import 'package:daleel/features/widgets/app_buttons/app_elevated_button.dart';
import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:daleel/apis/_base/dio_api_manager.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/core/widgets/base_stateful_screen_widget.dart';
import 'package:daleel/features/login/data/datasources/login_api_manager.dart';
import 'package:daleel/features/login/data/datasources/login_local_datasources.dart';
import 'package:daleel/features/login/data/models/login_response_model.dart';
import 'package:daleel/features/login/data/models/login_send_model.dart';
import 'package:daleel/features/login/data/repository/login_repository.dart';
import 'package:daleel/features/login/domain/usecases/login_use_case.dart';
import 'package:daleel/features/login/domain/usecases/login_validate_use_case.dart';
import 'package:daleel/features/login/domain/usecases/save_user_info_usecase.dart';
import 'package:daleel/features/login/domain/usecases/set_user_logged_in_usecase.dart';
import 'package:daleel/features/login/presentation/bloc/login_bloc.dart';
import 'package:daleel/features/widgets/text_field/app_text_form_filed_widget.dart';
import 'package:daleel/logic/user_manager.dart';
import 'package:daleel/preferences/preferences_manager.dart';
import 'package:daleel/utils/build_type/build_type.dart';
import 'package:daleel/utils/feedback/feedback_message.dart';
import 'package:daleel/utils/validations/app_validate.dart';
import 'package:go_router/go_router.dart';

import '../../../../res/app_colors.dart';

class LoginScreen extends StatelessWidget {
  LoginScreen({super.key});

  static const String routeName = '/login';

  final PreferencesManager preferencesManager = GetIt.I<PreferencesManager>();
  final DioApiManager dioApiManager = GetIt.I<DioApiManager>();

  @override
  Widget build(BuildContext context) {
    LoginRepository loginRepository = LoginRepository(
      LoginLocaleManager(preferencesManager),
      LoginApiManager(dioApiManager),
    );
    return BlocProvider<LoginBloc>(
      create:
          (BuildContext context) => LoginBloc(
            loginUseCase: LoginUseCase(loginRepository),
            loginValidateUseCase: LoginValidateUseCase(),
            saveUserInfoUseCase: SaveUserInfoUseCase(loginRepository),
            setUserLoggedInUseCase: SetUserLoggedInUseCase(loginRepository),
          ),
      child: const LoginScreenWithBloc(),
    );
  }
}

class LoginScreenWithBloc extends BaseStatefulScreenWidget {
  const LoginScreenWithBloc({super.key});

  @override
  BaseScreenState<LoginScreenWithBloc> baseScreenCreateState() =>
      _LoginScreenWithBlocState();
}

class _LoginScreenWithBlocState extends BaseScreenState<LoginScreenWithBloc>
    with AppValidate {
  AutovalidateMode _autoValidateMode = AutovalidateMode.disabled;
  UserMangers user = GetIt.I<UserMangers>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  String phone = "";
  String code = "";
  bool mobileSelected = false;
  TextEditingController passwordController = TextEditingController();
  TextEditingController userNameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (isDebugMode()) {
      _initialData();
    }
  }

  @override
  Widget baseScreenBuild(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<LoginBloc, LoginState>(
        listener: (context, state) async {
          if (state is LoginLoadingState) {
            showLoading();
          } else {
            hideLoading();
          }
          if (state is LoginNotValidatedState) {
            setState(() {
              _autoValidateMode = AutovalidateMode.onUserInteraction;
            });
          } else if (state is LoginValidatedState) {
            _loginEventApi();
          } else if (state is LoginSuccessfullyState) {
            _saveUserInfoEvent(state.loginResponseModel);
            _setLoggedInUserEvent();
          } else if (state is LoginErrorState) {
            showFeedbackMessage(
              state.isLocalizationKey
                  ? context.translate(state.errorMassage)
                  : state.errorMassage,
            );
            setState(() {
              _autoValidateMode = AutovalidateMode.onUserInteraction;
            });
          } else if (state is SavedUserInfoState) {
            AppRouter.mainNavigatorKey.currentContext?.go(MainTapsScreen.routeName);
          }
        },
        builder: (context, state) => buildLoginWidget(),
      ),
    );
  }

  Widget buildLoginWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      child: Form(
        key: _formKey,
        autovalidateMode: _autoValidateMode,
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              SizedBox(height: 70.h),
              Center(
                child: Image.asset(AppAssetPaths.daleel_splash, height: 96),
              ),
              SizedBox(height: 20.h),
              Center(
                child: Text(
                  context.translate(LocalizationKeys.login),
                  style: TextStyle(
                    fontSize: 24,
                    color: AppColors.buttonBlackTextColor,
                    fontFamily: 'DroidKufi',
                    fontWeight: FontWeight.w700,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: 40.h),
              _emailWidget(),
              SizedBox(height: 24.h),
              _passwordWidget(),
              SizedBox(height: 14.h),
              Row(
                children: [
                  Text(
                    'Forgot your password?',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'DroidKufi',
                      color: AppColors.forgetPassTitle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  TextButton(
                    style: ButtonStyle(
                      foregroundColor: WidgetStateProperty.all(AppColors.buttonBackground),
                      textStyle: WidgetStateProperty.all(
                        const TextStyle(fontWeight: FontWeight.w700, fontSize: 14),
                      ),
                      padding: WidgetStateProperty.all(EdgeInsets.zero),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      minimumSize: WidgetStateProperty.all(Size.zero),
                    ),
                    onPressed: () {},
                    child: const Text('Click here to reset it'),
                  ),
                ],
              ),
              SizedBox(height: 24.h),
              SizedBox(
                height: 50.h,
                width: double.infinity,
                child: AppElevatedButton.withTitle(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  onPressed: _getStartedClicked,
                  title: "Login",
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                ),
              ),
              SizedBox(height: 24.h),
              Center(
                child: Text(
                  'Don’t have an account?',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColors.headlineSmall,
                    fontFamily: 'DroidKufi',
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: 24.h),
              SizedBox(
                height: 50.h,
                width: double.infinity,
                child: AppElevatedButton.withTitle(
                  shape: RoundedRectangleBorder(
                    side: BorderSide(
                      color: AppColors.colorSecondary,
                      width: 1.5,
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  onPressed: _getStartedClicked,
                  color: Colors.transparent,
                  textColor: AppColors.colorSecondary,
                  title: "Create Account",
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                ),
              ),
              SizedBox(height: 24.h),
              Center(
                child: Text(
                  'Or through',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColors.headlineSmall,
                    fontFamily: 'DroidKufi',
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: 24.h),
              SizedBox(
                height: 50.h,
                width: double.infinity,
                child: AppElevatedButton.withTitleAndIcon(
                  onPressed: _getStartedClicked,
                  title: "Register with the UAE Pass digital identity",
                  icon: Icon(Icons.fingerprint, color: AppColors.whiteIcon),
                ),
              ),
              SizedBox(height: 24.h),
              Center(child: Image.asset(AppAssetPaths.spea_splash, height: 64)),
              SizedBox(height: 40.h),
              Center(
                child: Text(
                  '© All rights reserved to Sharjah Private Education Authority SPEA',
                  style: TextStyle(
                    fontSize: 10,
                    color: AppColors.headlineSmall,
                    fontFamily: 'DroidKufi',
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _emailWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "User Name *",
          style: TextStyle(
            fontSize: 16,
            color: AppColors.buttonBlackTextColor,
            fontFamily: 'DroidKufi',
            fontWeight: FontWeight.w400,
          ),
        ),
        const SizedBox(height: 10),
        AppTextFormField(
          hintText: "user name",
          fillColor: Colors.transparent,
          hintTextStyle: TextStyle(
            fontSize: 16,
            color: AppColors.formFieldHintText,
            fontFamily: 'DroidKufi',
            fontWeight: FontWeight.w400,
          ),
          controller: userNameController,
          validator: (value) => value?.isEmpty == true ? "Username is required" : null,
          enableBorderColor: AppColors.formFieldBorder,
        ),
      ],
    );
  }

  Widget _passwordWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Password *",
          style: TextStyle(
            fontSize: 16,
            color: AppColors.buttonBlackTextColor,
            fontFamily: 'DroidKufi',
            fontWeight: FontWeight.w400,
          ),
        ),
        const SizedBox(height: 10),
        AppTextFormField(
          hintText: "password",
          fillColor: Colors.transparent,
          hintTextStyle: TextStyle(
            fontSize: 16,
            color: AppColors.formFieldHintText,
            fontFamily: 'DroidKufi',
            fontWeight: FontWeight.w400,
          ),
          controller: passwordController,
          validator: passwordValidator,
          enableBorderColor: AppColors.formFieldBorder,
          obscure: true,
        ),
      ],
    );
  }

  ///////////////////////////////////////////////////////////
  /////////////////// Helper methods ////////////////////////
  ///////////////////////////////////////////////////////////

  LoginBloc get currentBloc => context.read<LoginBloc>();

  void _getStartedClicked() {
    FocusManager.instance.primaryFocus?.unfocus();
    currentBloc.add(ValidateLoginEvent(_formKey));
  }

  void _loginEventApi() {
    String userName = mobileSelected ? phone : userNameController.text;
    currentBloc.add(
      LoginApiEvent(
        LoginSendModel(userName: userName, password: passwordController.text),
      ),
    );
  }

  void _saveUserInfoEvent(LoginResponseModel loginResponseModel) {
    currentBloc.add(SaveUserInfoEvent(loginResponseModel));
  }

  void _setLoggedInUserEvent() {
    currentBloc.add(const SetLoggedInUserEvent());
  }

  void _initialData() {
    userNameController.text = "";
    passwordController.text = "";
  }
}
