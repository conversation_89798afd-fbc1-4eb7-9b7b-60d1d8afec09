import 'package:get_it/get_it.dart';
import 'package:daleel/features/login/data/models/login_response_model.dart';
import 'package:daleel/logic/user_manager.dart';
import 'package:daleel/preferences/preferences_manager.dart';

class LoginLocaleManager {
  final PreferencesManager preferencesManager;

  LoginLocaleManager(this.preferencesManager);

  final UserMangers userMangers = GetIt.I<UserMangers>();

  Future<void> setUserLogin() async {
    await preferencesManager.setLoggedIn();
    userMangers.setLoggedInUser();
  }

  Future<void> saveUserInfo(LoginResponseModel loginResponseModel) async {
    await preferencesManager.setAccessToken(loginResponseModel.token);
    await preferencesManager.setUserId(loginResponseModel.user.id.toString());
    // await preferencesManager.setUserFullNameEn(loginResponseModel.)
  }
}
