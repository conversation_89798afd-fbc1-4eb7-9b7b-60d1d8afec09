import 'package:daleel/apis/_base/dio_api_manager.dart';
import 'package:daleel/apis/api_urls.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/features/login/data/models/login_response_model.dart';
import 'package:daleel/features/login/data/models/login_send_model.dart';

class LoginApiManager {
  final DioApiManager dioApiManager;

  LoginApiManager(this.dioApiManager);

  Future<void> loginApi(
      LoginSendModel sendModel,
      void Function(LoginResponseModel) success,
      void Function(ErrorApiModel) fail) async {
    await dioApiManager.dioUnauthorized
        .post(ApiUrls.login, data: sendModel.toMap())
        .then((response) {
      Map<String, dynamic> extractedData =
          response.data as Map<String, dynamic>;
      // LoginResponseModel wrapper = LoginResponseModel.fromJson(extractedData["result"]);
      LoginResponseModel wrapper = LoginResponseModel.fromJson(extractedData);
      success(wrapper);
    }).catchError((error) {
      fail(ErrorApiModel.identifyError(error: error));
    });
  }
}
