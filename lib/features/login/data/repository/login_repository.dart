import 'package:daleel/features/login/data/datasources/login_api_manager.dart';
import 'package:daleel/features/login/data/datasources/login_local_datasources.dart';
import 'package:daleel/features/login/data/models/login_response_model.dart';
import 'package:daleel/features/login/data/models/login_send_model.dart';
import 'package:daleel/features/login/domain/repositories/base_login_repository.dart';

class LoginRepository implements BaseLoginRepository {
  final LoginLocaleManager loginLocaleManager;
  final LoginApiManager loginApiManager;

  LoginRepository(this.loginLocaleManager, this.loginApiManager);

  @override
  Future<LoginResponseModel> login(LoginSendModel login) async {
    late LoginResponseModel loginResponse;
    await loginApiManager.loginApi(login, (response) {
      loginResponse = response;
    }, (errorApiModel) {
      throw errorApiModel;
    });
    return loginResponse;
  }

  @override
  Future<void> setLoginUser() async {
    await loginLocaleManager.setUserLogin();
  }

  @override
  Future<void> saveUserInfo(LoginResponseModel loginResponseModel) async {
    await loginLocaleManager.saveUserInfo(loginResponseModel);
  }

}
