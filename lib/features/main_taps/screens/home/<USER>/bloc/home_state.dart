part of 'home_bloc.dart';

abstract class HomeState extends Equatable {
  const HomeState();

  @override
  List<Object> get props => [];
}

class HomeInitialState extends HomeState {}

class HomeLoadingState extends HomeState {}

class HomeLoadedState extends HomeState {
  final RequestEnumValues requestEnumValues;

  const HomeLoadedState({required this.requestEnumValues});

  @override
  List<Object> get props => [requestEnumValues];
}

class HomeErrorState extends HomeState {
  final String errorMessage;
  final bool isLocalizationKey;

  const HomeErrorState(this.errorMessage, this.isLocalizationKey);

  @override
  List<Object> get props => [errorMessage, isLocalizationKey];
}
