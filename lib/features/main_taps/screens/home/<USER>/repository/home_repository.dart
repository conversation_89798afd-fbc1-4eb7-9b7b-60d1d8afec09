import 'package:daleel/features/main_taps/screens/home/<USER>/datasources/home_api_manager.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/current_school_year_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/repositories/base_home_repository.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_enums_value.dart';

// class HomeRepository implements BaseHomeRepository {
//   final HomeApiManager homeApiManager;

//   HomeRepository(this.homeApiManager);

//   @override
//   Future<CurrentSchoolYearModel> getCurrentSchoolYear() async {
//     late CurrentSchoolYearModel response;
//     await homeApiManager.getCurrentSchoolYearApi((data) {
//       response = CurrentSchoolYearModel.fromJson(data);
//     }, (errorApiModel) {
//       throw errorApiModel;
//     });
//     return response;
//   }

//   @override
//   Future<RequestEnumValues> getRequestEnumValues() async {
//     late RequestEnumValues response;
//     await homeApiManager.getRequestEnumValuesApi((data) {
//       response = RequestEnumValues.fromJson(data);
//     }, (errorApiModel) {
//       throw errorApiModel;
//     });
//     return response;
//   }
// }


abstract class BaseHomeRepository {
  Future<RequestEnumValues> getRequestEnumValues();
}

class HomeRepository implements BaseHomeRepository {
  final HomeApiManager homeApiManager;

  HomeRepository(this.homeApiManager);

  @override
  Future<CurrentSchoolYearModel> getCurrentSchoolYear() async {
    late CurrentSchoolYearModel response;
    await homeApiManager.getCurrentSchoolYearApi((data) {
      response = CurrentSchoolYearModel.fromJson(data);
    }, (errorApiModel) {
      throw errorApiModel;
    });
    return response;
  }

  @override
  Future<RequestEnumValues> getRequestEnumValues() async {
    try {
      late RequestEnumValues response;
      await homeApiManager.getRequestEnumValuesApi(
        (data) => response = RequestEnumValues.fromJson(data),
        (error) => throw error,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }
}
