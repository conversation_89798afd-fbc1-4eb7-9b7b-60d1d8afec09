import 'package:daleel/features/main_taps/screens/home/<USER>/models/current_school_year_model.dart';
import 'package:dartz/dartz.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/core/usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/repository/home_repository.dart';

class GetCurrentSchoolYearUseCase extends BaseUseCase<CurrentSchoolYearModel, NoParams> {
  HomeRepository homeRepository;

  GetCurrentSchoolYearUseCase(this.homeRepository);

  @override
  Future<Either<ErrorApiModel, CurrentSchoolYearModel>> call(NoParams params) async {
    try {
      final response = await homeRepository.getCurrentSchoolYear();
      return Right(response);
    } catch (onError) {
      ErrorApiModel errorApiModel = onError as ErrorApiModel;
      return Left(errorApiModel);
    }
  }
}
