import 'package:daleel/apis/_base/dio_api_manager.dart';
import 'package:daleel/apis/api_urls.dart';
import 'package:daleel/apis/errors/error_api_model.dart';

class HomeApiManager {
  final DioApiManager dioApiManager;

  HomeApiManager(this.dioApiManager);

  Future<void> getCurrentSchoolYearApi(
      void Function(Map<String, dynamic>) success,
      void Function(ErrorApiModel) fail) async {
    await dioApiManager.dio
        .get(ApiUrls.currentSchoolYear)
        .then((response) {
      Map<String, dynamic> extractedData =
          response.data as Map<String, dynamic>;
      success(extractedData);
    }).catchError((error) {
      fail(ErrorApiModel.identifyError(error: error));
    });
  }

  Future<void> getRequestEnumValuesApi(
      void Function(Map<String, dynamic>) success,
      void Function(ErrorApiModel) fail) async {
    await dioApiManager.dio
        .get(ApiUrls.requestEnumValues)
        .then((response) {
      Map<String, dynamic> extractedData =
          response.data as Map<String, dynamic>;
      success(extractedData);
    }).catchError((error) {
      fail(ErrorApiModel.identifyError(error: error));
    });
  }
}