class CurrentSchoolYearModel {
    final int id;
    final Name name;

    CurrentSchoolYearModel({
        required this.id,
        required this.name,
    });

    factory CurrentSchoolYearModel.fromJson(Map<String, dynamic> json) {
        return CurrentSchoolYearModel(
            id: json['id'] as int,
            name: Name.from<PERSON><PERSON>(json['name']),
        );
    }
}

class Name {
    final String en;
    final String ar;

    Name({
        required this.en,
        required this.ar,
    });

    factory Name.fromJson(Map<String, dynamic> json) {
        return Name(
            en: json['en'] as String,
            ar: json['ar'] as String,
        );
    }
}
