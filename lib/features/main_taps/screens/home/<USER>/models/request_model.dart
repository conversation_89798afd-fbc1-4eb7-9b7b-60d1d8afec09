
class Request {
  final String id;
  final String requestType;
  final String requestStatus;
  final String requestNumber;
  final String relatedTo;
  final String school;
  final String curriculum;
  final String createdBy;
  final String requestDate;
  final String modificationDate;
  bool isExpanded;

  Request({
    required this.id,
    required this.requestType,
    required this.requestStatus,
    required this.requestNumber,
    required this.relatedTo,
    required this.school,
    required this.curriculum,
    required this.createdBy,
    required this.requestDate,
    required this.modificationDate,
    this.isExpanded = false,
  });
}
