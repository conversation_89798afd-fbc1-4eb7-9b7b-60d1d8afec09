
import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CertificateWidget extends StatelessWidget {
  const CertificateWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      decoration: BoxDecoration(
        color: AppColors.colorCertificateBackground,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(AppAssetPaths.certificate, height: 40, width: 40),

          SizedBox(width: 10.w), // ← هنا التعديل كان غلط مستخدم height بدل width

          Column(
            mainAxisAlignment: MainAxisAlignment.center, // ← عشان الكلام يكون في النص رأسيًا
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Evaluation Certificate',
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 14,
                  color: AppColors.headlineSmall,
                ),
              ),
              Text(
                'Mohamed Ahmed Mahmoud',
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: AppColors.headlineSmall,
                ),
              ),
            ],
          ),

          Spacer(),

          Container(
            width: 60,
            height: 40,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.r),
              color: AppColors.colorSecondary,
            ),
            child: Center(
              child: Text(
                'Pay',
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: AppColors.whiteIcon,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}