import 'package:daleel/apis/_base/dio_api_manager.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/core/widgets/base_stateful_screen_widget.dart';
import 'package:daleel/features/main_taps/Screens/home/<USER>/bloc/home_bloc.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/datasources/home_api_manager.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/current_school_year_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_enums_value.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/repository/home_repository.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_current_school_year_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_request_enum_values_usecase.dart';
import 'package:daleel/features/widgets/app_buttons/app_elevated_button.dart';
import 'package:daleel/preferences/preferences_manager.dart';
import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/feedback/feedback_message.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import '../widgets/buildـHomeـStatCard.dart';
import '../widgets/certificate_widget.dart';
import '../widgets/user_header.dart';

class HomeScreen extends StatelessWidget {
  HomeScreen({super.key});

  final PreferencesManager preferencesManager = GetIt.I<PreferencesManager>();
  final DioApiManager dioApiManager = GetIt.I<DioApiManager>();

  @override
Widget build(BuildContext context) {
  HomeRepository homeRepository = HomeRepository(
    HomeApiManager(dioApiManager),
  );

  // final getCurrentSchoolYearUseCase = GetCurrentSchoolYearUseCase(homeRepository);
  // final getRequestEnumValuesUseCase = GetRequestEnumValuesUseCase(homeRepository);

  return BlocProvider(
      create: (context) => HomeBloc(
        getRequestEnumValuesUseCase: getIt<GetRequestEnumValuesUseCase>(),
      )..add(LoadHomeDataEvent()),
      child: const HomeScreenWithBloc(),
    );
}
}

class HomeScreenWithBloc extends BaseStatefulScreenWidget {
  const HomeScreenWithBloc({super.key});

  @override
  BaseScreenState<HomeScreenWithBloc> baseScreenCreateState() =>
      _HomeScreenWithBlocState();
}

class _HomeScreenWithBlocState extends BaseScreenState<HomeScreenWithBloc> {
  final List<Request> requests = [
    Request(
      id: '1',
      requestType: 'Issuance of Academic Sequence Certificate',
      requestStatus: 'Pending',
      requestNumber: '207',
      relatedTo: 'Mohammed Ahmed',
      school: 'Al-Zuhur Educational',
      curriculum: 'British',
      createdBy: 'Mohammed Masoud Ahmed',
      requestDate: '22 January 2025',
      modificationDate: '22 January 2025',
    ),
    Request(
      id: '2',
      requestType: 'Transcript Request',
      requestStatus: 'Approved',
      requestNumber: '208',
      relatedTo: 'Ahmed Hassan',
      school: 'Al-Noor International',
      curriculum: 'American',
      createdBy: 'Admin User',
      requestDate: '23 January 2025',
      modificationDate: '23 January 2025',
    ),
    Request(
      id: '3',
      requestType: 'Enrollment Verification',
      requestStatus: 'Rejected',
      requestNumber: '209',
      relatedTo: 'Fatima Ali',
      school: 'Knowledge Academy',
      curriculum: 'IB',
      createdBy: 'Fatima Ali',
      requestDate: '24 January 2025',
      modificationDate: '24 January 2025',
    ),
    Request(
      id: '4',
      requestType: 'Transfer Certificate',
      requestStatus: 'Completed',
      requestNumber: '210',
      relatedTo: 'Omar Khaled',
      school: 'Future Leaders School',
      curriculum: 'National',
      createdBy: 'Omar Khaled',
      requestDate: '25 January 2025',
      modificationDate: '25 January 2025',
    ),
    Request(
      id: '5',
      requestType: 'Course Completion Certificate',
      requestStatus: 'Processing',
      requestNumber: '211',
      relatedTo: 'Layla Mahmoud',
      school: 'Science Academy',
      curriculum: 'British',
      createdBy: 'Layla Mahmoud',
      requestDate: '26 January 2025',
      modificationDate: '26 January 2025',
    ),
  ];

  void _toggleExpand(String id) {
    setState(() {
      for (var request in requests) {
        if (request.id == id) {
          request.isExpanded = !request.isExpanded;
        } else {
          request.isExpanded = false;
        }
      }
    });
  }

  @override
  Widget baseScreenBuild(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is HomeLoadingState) {
            showLoading();
          } else {
            hideLoading();
          }
          if (state is HomeErrorState) {
            showFeedbackMessage(state.errorMessage);
          }
        },
        builder: (context, state) => _buildHomeWidget(state),
      ),
    );
  }

  Widget _buildHomeWidget(HomeState state) {
    return SingleChildScrollView(
      child: Column(
        children: [
          const UserHeader(),
          SizedBox(height: 20.h),
          _buildStatsSection(state),
          _buildButtonsSection(),
          _buildCertificatesSection(),
          _buildRequestsSection(),
        ],
      ),
    );
  }

  Widget _buildStatsSection(HomeState state) {
    if (state is HomeLoadedState) {
      return Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                buildHomeStatCard(
                  color: AppColors.colorHomeGreenButton,
                  text: context.translate(LocalizationKeys.registeredChildren),
                  imagePath: AppAssetPaths.ic_Open_enrollment,
                  textColor: AppColors.whiteIcon,
                ),
                SizedBox(width: 20.w),
                buildHomeStatCard(
                  color: AppColors.colorHomeButtons.withOpacity(0.2),
                  text: context.translate(
                    LocalizationKeys.unregisteredChildren,
                  ),
                  textColor: AppColors.colorHomeButtons,
                  imagePath: AppAssetPaths.ic_No_id_card,
                ),
              ],
            ),
          ),
          SizedBox(height: 20.h),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                buildHomeStatCard(
                  color: AppColors.colorHomeButtons.withOpacity(0.2),
                  text: context.translate(LocalizationKeys.sonsWithdrawn),
                  textColor: AppColors.colorHomeButtons,
                  imagePath: AppAssetPaths.ic_Unsubscribe,
                ),
                SizedBox(width: 20.w),
                buildHomeStatCard(
                  color: AppColors.colorHomeButtons.withOpacity(0.2),
                  text: context.translate(
                    LocalizationKeys.sonsFinishedStudying,
                  ),
                  textColor: AppColors.colorHomeButtons,
                  imagePath: AppAssetPaths.ic_finish,
                ),
              ],
            ),
          ),
        ],
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildButtonsSection() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        children: [
          SizedBox(
            height: 50.h,
            width: double.infinity,
            child: AppElevatedButton.withTitle(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              onPressed: _onAddNewSonPressed,
              title: context.translate(LocalizationKeys.addNewSon),
              fontWeight: FontWeight.w400,
              fontSize: 14,
            ),
          ),
          SizedBox(height: 20.h),
          SizedBox(
            height: 50.h,
            width: double.infinity,
            child: AppElevatedButton.withTitle(
              shape: RoundedRectangleBorder(
                side: BorderSide(color: AppColors.colorSecondary, width: 1.5),
                borderRadius: BorderRadius.circular(16),
              ),
              onPressed: _onDelegationPressed,
              color: Colors.transparent,
              textColor: AppColors.colorSecondary,
              title: context.translate(LocalizationKeys.delegation),
              fontWeight: FontWeight.w400,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCertificatesSection() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Text(
                context.translate(LocalizationKeys.certificates),
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 16,
                  color: AppColors.colorSecondary,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: _onShowAllPressed,
                child: Container(
                  width: 100,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: AppColors.colorshowAll.withOpacity(0.2),
                  ),
                  child: Center(
                    child: Text(
                      context.translate(LocalizationKeys.showAll),
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        color: AppColors.colorSecondary,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 10.h),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: List.generate(
              5,
              (index) => Padding(
                padding: const EdgeInsets.only(bottom: 20),
                child: const CertificateWidget(),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRequestsSection() {
    return Column(
      children: [
        // Header with title and show all
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Text(
                context.translate(LocalizationKeys.requests),
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 16,
                  color: AppColors.colorSecondary,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: _onShowAllPressed,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: AppColors.colorshowAll.withOpacity(0.2),
                  ),
                  child: Text(
                    context.translate(LocalizationKeys.showAll),
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                      color: AppColors.colorSecondary,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16.h),

        // Header with title
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              // Request type dropdown
              Expanded(
                child: Container(
                  // padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Row(
                    children: [
                      Text(
                        context.translate(LocalizationKeys.requestType),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(width: 12.w),

              // Request status dropdown
              Expanded(
                child: Container(
                  child: Row(
                    children: [
                      Text(
                        context.translate(LocalizationKeys.requestStatus),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 10.h),

        // Filters section
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              // Request type dropdown
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Text(
                        context.translate(LocalizationKeys.requestType),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const Spacer(),
                      Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.grey.shade600,
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(width: 12.w),

              // Request status dropdown
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Text(
                        context.translate(LocalizationKeys.requestStatus),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const Spacer(),
                      Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.grey.shade600,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16.h),

        // Action buttons
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              // Search button
              Expanded(
                child: Container(
                  height: 48.h,
                  decoration: BoxDecoration(
                    color: AppColors.colorSecondary,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: Text(
                      context.translate(LocalizationKeys.search),
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12.w),

              // Reset button
              Expanded(
                child: Container(
                  height: 48.h,
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.colorSecondary),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: Text(
                      context.translate(LocalizationKeys.reset),
                      style: TextStyle(
                        color: AppColors.colorSecondary,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Request cards
        Container(
          color: Colors.white,
          child: ListView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: requests.length,
            itemBuilder: (context, index) {
              final request = requests[index];
              return _buildRequestItem(request);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRequestItem(Request request) {
    return Card(
      color: AppColors.colorCertificateBackground,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () => _toggleExpand(request.id),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header (always visible)
              Row(
                children: [
                  Image.asset(
                    AppAssetPaths.ic_request_icon,
                    width: 24,
                    height: 24,
                  ),
                  SizedBox(width: 20.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          request.requestType,
                          style: const TextStyle(
                            color: AppColors.appBarBackground,
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    request.isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: AppColors.appBarBackground,
                  ),
                ],
              ),

              // Expandable details
              if (request.isExpanded) ...[
                const Divider(height: 24),
                _buildDetailRow('Request Number', request.requestNumber , false , true),
                _buildDetailRow('Request related to', request.relatedTo, false, false),
                _buildDetailRow('School', request.school, false, false),
                _buildDetailRow('Curriculum', request.curriculum, false, false),
                _buildDetailRow('Created by', request.createdBy, false, false),
                _buildDetailRow('Request Date', request.requestDate, false, false),
                _buildDetailRow('Modification Date', request.modificationDate, true, false),
                SizedBox(height: 8.h),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value , bool isDetails , bool isStatus) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 12,
                color: AppColors.mainGray,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 12,
                color: AppColors.mainGray,
              ),
            ),
          ),
          isStatus ? Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              context.translate(LocalizationKeys.pending),
              style: TextStyle(
                fontSize: 12,
                color: Colors.blue.shade700,
              ),
            ),
          ) : const SizedBox.shrink(),
          isDetails ? Container( 
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.colorSecondary),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                context.translate(LocalizationKeys.details),
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  color: AppColors.colorSecondary,
                  fontSize: 12,
                ),
              ),
            ) : const SizedBox.shrink(),
        ],
      ),
    );
  }

  HomeBloc get currentBloc => context.read<HomeBloc>();

  void _onAddNewSonPressed() {
    // TODO: Navigate to add new son screen
  }

  void _onDelegationPressed() {
    // TODO: Navigate to delegation screen
  }

  void _onShowAllPressed() {
    // TODO: Navigate to all certificates screen
  }
}
