import 'package:get_it/get_it.dart';
import 'package:daleel/preferences/preferences_manager.dart';
import 'package:daleel/utils/locale/locale_cubit.dart';

class UserMangers {
  final PreferencesManager preferencesManager;

  UserMangers(this.preferencesManager);

  bool _isGustUser = true;
  bool _isLocaleEmarat = false;
  String _nameEn = '';
  String _nameAr = '';
  String _imageUrl = '';
  String _email = '';
  String _position = '';
  String _phoneNumber = '';
  String _userId = '';
  String? token;
  String? refreshToken;

  LocaleApp _localeApp = LocaleApp.en;
  bool _isVerifiedUser = false;

  bool get isVerifiedUser => _isVerifiedUser;

  set isVerifiedUser(bool value) {
    _isVerifiedUser = value;
  }

  bool isGustUser() {
    return _isGustUser;
  }

  bool isLocaleEmarat() {
    return _isLocaleEmarat;
  }

  String getUserName(bool isEnglish) {
    return isEnglish ? _nameEn : _nameAr;
  }

  void setLocaleApp(LocaleApp localeApp) {
    _localeApp = localeApp;
  }

  LocaleApp getLocaleApp() {
    return _localeApp;
  }

  void setUserName({required String userNameEn, required String userNameAr}) {
    _nameEn = userNameEn;
    _nameAr = userNameAr;
  }

  void setImageUrl(String imageUrl) {
    _imageUrl = imageUrl;
  }

  String getImageUrl() {
    return _imageUrl;
  }


  void setEmail(String email) {
    _email = email;
  }

  String getEmail() {
    return _email;
  }

  void setPosition(String position) {
    _position = position;
  }

  String getPosition() {
    return _position;
  }

  void setPhoneNumber(String phoneNumber) {
    _phoneNumber = phoneNumber;
  }

  String getPhoneNumber() {
    return _phoneNumber;
  }

  String getUserId() {
    return _userId;
  }

  void setLoggedInUser() {
    _isGustUser = false;
  }

  void setIsLocaleEmarat(bool isLocaleEmarat) {
    _isLocaleEmarat = isLocaleEmarat;
  }

  Future<String> getUserFullName() async {
    return (await preferencesManager.getUserFullNameEn()) ?? '';
  }

  Future<void> setUserInfoAndModeFromLocal() async {
    _isGustUser = !await preferencesManager.isLoggedIn();
    _nameEn = await preferencesManager.getUserFullNameEn() ?? '';
    _nameAr = await preferencesManager.getUserFullNameAr() ?? '';
    _isLocaleEmarat = await preferencesManager.isLocaleEmarat();
    _phoneNumber = await preferencesManager.getPhone() ?? '';
    _email = await preferencesManager.getEmail() ?? '';
    _imageUrl = await preferencesManager.getProfileImage() ?? '';
    _userId = await preferencesManager.getUserId() ?? '';
  }

  Future<void> signOut() async {
    _isGustUser = true;
    _isLocaleEmarat = false;
    _nameEn = '';
    _nameAr = '';
    _phoneNumber = '';
    _email = '';
    _imageUrl = '';
    token = "";
    refreshToken = "";

    await GetIt.I<PreferencesManager>().clearData();
    await GetIt.I<PreferencesManager>().setLogout();
    // AppRouter.mainNavigatorKey.currentState!
    //     .pushNamedAndRemoveUntil(LoginScreen.routeName, (_) => false);
  }
}
